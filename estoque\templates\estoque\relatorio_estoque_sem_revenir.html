{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Relatório de Estoque Sem Revenir{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i> Relatório de Estoque Sem Revenir
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Formulário de Exportação -->
                    <form method="post" class="mb-4">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-8">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    Este relatório apresenta todas as molas em estoque que ainda não passaram pelo processo de revenimento.
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2">
                                    <button type="submit" name="formato" value="pdf" class="btn btn-danger">
                                        <i class="fas fa-file-pdf"></i> Exportar PDF
                                    </button>
                                    <button type="submit" name="formato" value="csv" class="btn btn-success">
                                        <i class="fas fa-file-csv"></i> Exportar CSV
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if estoques %}
                        <!-- Estatísticas -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info">
                                        <i class="fas fa-cubes"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total de Molas</span>
                                        <span class="info-box-number">{{ total_molas }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning">
                                        <i class="fas fa-calculator"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Quantidade Total</span>
                                        <span class="info-box-number">{{ total_quantidade }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tabela de Dados -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Código</th>
                                        <th>Cliente</th>
                                        <th>Quantidade</th>
                                        <th>Última Movimentação</th>
                                        <th>Observações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for estoque in estoques %}
                                        <tr>
                                            <td>
                                                <strong>{{ estoque.mola.codigo }}</strong>
                                            </td>
                                            <td>{{ estoque.mola.cliente }}</td>
                                            <td>
                                                <span class="badge bg-primary">{{ estoque.quantidade }}</span>
                                            </td>
                                            <td>{{ estoque.data_ultima_movimentacao|date:"d/m/Y H:i" }}</td>
                                            <td>
                                                {% if estoque.observacoes %}
                                                    {{ estoque.observacoes }}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            Nenhuma mola encontrada no estoque sem revenir.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
