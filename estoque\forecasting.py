"""
Módulo para previsão de demanda avançada usando modelos estatísticos e de machine learning
"""
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Sum, Count, F, Q
from .models import MovimentacaoEstoque, Mola, PrevisaoDemanda

# Configurar logger
logger = logging.getLogger(__name__)

# Verificar se as bibliotecas avançadas estão instaladas
STATSMODELS_INSTALLED = False
PROPHET_INSTALLED = False
SKLEARN_INSTALLED = False

try:
    import statsmodels.api as sm
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.statespace.sarimax import SARIMAX
    STATSMODELS_INSTALLED = True
    logger.info("Statsmodels encontrado e carregado com sucesso.")
except ImportError:
    logger.warning("Statsmodels não está instalado. Alguns modelos de previsão não estarão disponíveis.")

try:
    from prophet import Prophet
    PROPHET_INSTALLED = True
    logger.info("Prophet encontrado e carregado com sucesso.")
except ImportError:
    logger.warning("Prophet não está instalado. Alguns modelos de previsão não estarão disponíveis.")

try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    SKLEARN_INSTALLED = True
    logger.info("Scikit-learn encontrado e carregado com sucesso.")
except ImportError:
    logger.warning("Scikit-learn não está instalado. Alguns modelos de previsão não estarão disponíveis.")


def get_historical_data(mola_id, days=365, daily=False):
    """
    Obtém dados históricos de vendas para uma mola (incluindo movimentações e itens de pedido)

    Args:
        mola_id: ID da mola
        days: Número de dias para obter dados históricos
        daily: Se True, retorna dados diários, caso contrário, mensais

    Returns:
        DataFrame com dados históricos
    """
    # Definir data de início
    start_date = timezone.now().date() - timedelta(days=days)

    # Obter movimentações de saída (vendas via estoque)
    movimentacoes = MovimentacaoEstoque.objects.filter(
        mola_id=mola_id,
        tipo='S',
        data__gte=start_date
    ).order_by('data')

    # Obter itens de pedido atendidos (vendas sem movimentar estoque)
    from .models import ItemPedido
    itens_pedido = ItemPedido.objects.filter(
        mola_id=mola_id,
        atendido=True,
        pedido__data_pedido__gte=start_date
    ).select_related('pedido').order_by('pedido__data_pedido')

    # Combinar os dados de vendas
    data = []

    # Adicionar movimentações de estoque
    for mov in movimentacoes:
        data.append({
            'data': mov.data.date() if hasattr(mov.data, 'date') else mov.data,
            'quantidade': mov.quantidade
        })

    # Adicionar itens de pedido
    for item in itens_pedido:
        data.append({
            'data': item.pedido.data_pedido.date() if hasattr(item.pedido.data_pedido, 'date') else item.pedido.data_pedido,
            'quantidade': item.quantidade
        })

    if not data:
        logger.warning(f"Sem dados históricos de vendas para a mola ID {mola_id}")
        return None
    
    df = pd.DataFrame(data)
    
    # Agrupar por dia ou mês
    if daily:
        df = df.groupby('data').sum().reset_index()
    else:
        # Extrair ano e mês
        df['ano_mes'] = df['data'].apply(lambda x: f"{x.year}-{x.month:02d}")
        df = df.groupby('ano_mes').sum().reset_index()
        # Converter ano_mes para data (primeiro dia do mês)
        df['data'] = df['ano_mes'].apply(lambda x: datetime.strptime(f"{x}-01", "%Y-%m-%d"))
        df = df.drop('ano_mes', axis=1)
    
    # Ordenar por data
    df = df.sort_values('data')
    
    # Preencher datas faltantes com zeros
    if daily:
        # Criar intervalo completo de datas
        date_range = pd.date_range(start=df['data'].min(), end=df['data'].max(), freq='D')
        # Criar DataFrame com todas as datas
        full_df = pd.DataFrame({'data': date_range})
        # Mesclar com dados existentes
        df = pd.merge(full_df, df, on='data', how='left')
        df['quantidade'] = df['quantidade'].fillna(0)
    else:
        # Criar intervalo completo de meses
        date_range = pd.date_range(start=df['data'].min(), end=df['data'].max(), freq='MS')
        # Criar DataFrame com todos os meses
        full_df = pd.DataFrame({'data': date_range})
        # Mesclar com dados existentes
        df = pd.merge(full_df, df, on='data', how='left')
        df['quantidade'] = df['quantidade'].fillna(0)
    
    return df


def detect_seasonality(data):
    """
    Detecta sazonalidade nos dados
    
    Args:
        data: DataFrame com dados históricos
        
    Returns:
        Dicionário com informações de sazonalidade
    """
    if not STATSMODELS_INSTALLED or data is None or len(data) < 12:
        return {'has_seasonality': False}
    
    try:
        # Converter para série temporal
        ts = pd.Series(data['quantidade'].values, index=data['data'])
        
        # Decomposição da série temporal
        decomposition = sm.tsa.seasonal_decompose(ts, model='additive', period=12)
        
        # Calcular a força da sazonalidade
        seasonal_strength = np.std(decomposition.seasonal) / np.std(decomposition.trend + decomposition.seasonal)
        
        # Determinar se há sazonalidade significativa
        has_seasonality = seasonal_strength > 0.3
        
        return {
            'has_seasonality': has_seasonality,
            'seasonal_strength': seasonal_strength,
            'period': 12
        }
    except Exception as e:
        logger.error(f"Erro ao detectar sazonalidade: {str(e)}")
        return {'has_seasonality': False}


def forecast_arima(data, periods=12):
    """
    Realiza previsão usando modelo ARIMA
    
    Args:
        data: DataFrame com dados históricos
        periods: Número de períodos para prever
        
    Returns:
        DataFrame com previsões
    """
    if not STATSMODELS_INSTALLED or data is None or len(data) < 12:
        logger.warning("Não é possível usar ARIMA: statsmodels não instalado ou dados insuficientes")
        return None
    
    try:
        # Converter para série temporal
        ts = pd.Series(data['quantidade'].values, index=data['data'])
        
        # Detectar sazonalidade
        seasonality = detect_seasonality(data)
        
        # Escolher modelo com base na sazonalidade
        if seasonality['has_seasonality']:
            # Usar SARIMA para dados sazonais
            model = SARIMAX(
                ts,
                order=(1, 1, 1),
                seasonal_order=(1, 1, 1, 12),
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            logger.info("Usando modelo SARIMA para dados sazonais")
        else:
            # Usar ARIMA para dados não sazonais
            model = ARIMA(ts, order=(1, 1, 1))
            logger.info("Usando modelo ARIMA para dados não sazonais")
        
        # Ajustar modelo
        model_fit = model.fit(disp=False)
        
        # Fazer previsão
        forecast = model_fit.forecast(steps=periods)
        
        # Criar DataFrame com previsões
        last_date = data['data'].max()
        forecast_dates = [last_date + timedelta(days=30 * (i + 1)) for i in range(periods)]
        
        forecast_df = pd.DataFrame({
            'data': forecast_dates,
            'quantidade': np.maximum(0, forecast.values.round())  # Garantir que não haja valores negativos
        })
        
        return forecast_df
    except Exception as e:
        logger.error(f"Erro ao realizar previsão ARIMA: {str(e)}")
        return None


def forecast_prophet(data, periods=12):
    """
    Realiza previsão usando modelo Prophet
    
    Args:
        data: DataFrame com dados históricos
        periods: Número de períodos para prever
        
    Returns:
        DataFrame com previsões
    """
    if not PROPHET_INSTALLED or data is None or len(data) < 3:
        logger.warning("Não é possível usar Prophet: biblioteca não instalada ou dados insuficientes (mínimo: 3 pontos)")
        return None
    
    try:
        # Preparar dados para Prophet (requer colunas 'ds' e 'y')
        prophet_data = data.rename(columns={'data': 'ds', 'quantidade': 'y'})
        
        # Inicializar modelo
        model = Prophet(
            yearly_seasonality=True,
            weekly_seasonality=False,
            daily_seasonality=False,
            seasonality_mode='multiplicative',
            interval_width=0.95
        )
        
        # Adicionar sazonalidade mensal
        model.add_seasonality(name='monthly', period=30.5, fourier_order=5)
        
        # Ajustar modelo
        model.fit(prophet_data)
        
        # Criar DataFrame para previsão
        future = model.make_future_dataframe(periods=periods, freq='MS')
        
        # Fazer previsão
        forecast = model.predict(future)
        
        # Filtrar apenas os períodos futuros
        forecast = forecast[forecast['ds'] > data['data'].max()]
        
        # Criar DataFrame com previsões
        forecast_df = pd.DataFrame({
            'data': forecast['ds'].dt.date,
            'quantidade': np.maximum(0, forecast['yhat'].values.round())  # Garantir que não haja valores negativos
        })
        
        return forecast_df
    except Exception as e:
        logger.error(f"Erro ao realizar previsão Prophet: {str(e)}")
        return None


def forecast_ensemble(mola_id, periods=12, method='auto'):
    """
    Realiza previsão usando um conjunto de modelos
    
    Args:
        mola_id: ID da mola
        periods: Número de períodos para prever
        method: Método de previsão ('auto', 'arima', 'prophet', 'ensemble')
        
    Returns:
        Tupla (previsões, precisão, método usado)
    """
    # Obter dados históricos
    data = get_historical_data(mola_id)
    
    if data is None or len(data) < 3:
        logger.warning(f"Dados históricos insuficientes para a mola ID {mola_id} (mínimo: 3 pontos, encontrados: {len(data) if data is not None else 0})")
        return None, 0, 'Dados insuficientes'
    
    # Determinar o método de previsão
    if method == 'auto':
        # Escolher o melhor método com base nos dados
        if len(data) >= 24 and STATSMODELS_INSTALLED and detect_seasonality(data)['has_seasonality']:
            method = 'arima'
        elif PROPHET_INSTALLED:
            method = 'prophet'
        elif STATSMODELS_INSTALLED:
            method = 'arima'
        else:
            method = 'media_movel'
    
    # Realizar previsão com o método escolhido
    if method == 'arima' and STATSMODELS_INSTALLED:
        forecast_df = forecast_arima(data, periods)
        method_name = 'ARIMA/SARIMA'
    elif method == 'prophet' and PROPHET_INSTALLED:
        forecast_df = forecast_prophet(data, periods)
        method_name = 'Prophet'
    elif method == 'ensemble' and STATSMODELS_INSTALLED and PROPHET_INSTALLED:
        # Combinar previsões de múltiplos modelos
        arima_forecast = forecast_arima(data, periods)
        prophet_forecast = forecast_prophet(data, periods)
        
        if arima_forecast is not None and prophet_forecast is not None:
            # Calcular média das previsões
            forecast_df = pd.DataFrame({
                'data': arima_forecast['data'],
                'quantidade': ((arima_forecast['quantidade'] + prophet_forecast['quantidade']) / 2).round()
            })
            method_name = 'Ensemble (ARIMA + Prophet)'
        elif arima_forecast is not None:
            forecast_df = arima_forecast
            method_name = 'ARIMA/SARIMA'
        elif prophet_forecast is not None:
            forecast_df = prophet_forecast
            method_name = 'Prophet'
        else:
            # Fallback para média móvel
            forecast_df = forecast_moving_average(data, periods)
            method_name = 'Média Móvel'
    else:
        # Fallback para média móvel
        forecast_df = forecast_moving_average(data, periods)
        method_name = 'Média Móvel'
    
    if forecast_df is None:
        logger.warning(f"Não foi possível gerar previsão para a mola ID {mola_id}")
        return None, 0, 'Falha na previsão'
    
    # Calcular precisão (usando dados históricos)
    accuracy = calculate_forecast_accuracy(data, method)
    
    return forecast_df, accuracy, method_name


def forecast_moving_average(data, periods=12, window=3):
    """
    Realiza previsão usando média móvel
    
    Args:
        data: DataFrame com dados históricos
        periods: Número de períodos para prever
        window: Tamanho da janela para média móvel
        
    Returns:
        DataFrame com previsões
    """
    if data is None or len(data) < window:
        return None
    
    try:
        # Calcular média móvel
        avg = data['quantidade'].tail(window).mean()
        
        # Criar DataFrame com previsões
        last_date = data['data'].max()
        forecast_dates = [last_date + timedelta(days=30 * (i + 1)) for i in range(periods)]
        
        forecast_df = pd.DataFrame({
            'data': forecast_dates,
            'quantidade': [round(avg)] * periods
        })
        
        return forecast_df
    except Exception as e:
        logger.error(f"Erro ao realizar previsão com média móvel: {str(e)}")
        return None


def calculate_forecast_accuracy(data, method):
    """
    Calcula a precisão do modelo de previsão usando validação cruzada
    
    Args:
        data: DataFrame com dados históricos
        method: Método de previsão
        
    Returns:
        Precisão do modelo (0-100%)
    """
    if data is None or len(data) < 12:
        return 0
    
    try:
        # Dividir dados em treino e teste (últimos 3 meses para teste)
        train_data = data.iloc[:-3]
        test_data = data.iloc[-3:]
        
        if len(train_data) < 3:
            return 0
        
        # Realizar previsão com dados de treino
        if method == 'arima' and STATSMODELS_INSTALLED:
            forecast_df = forecast_arima(train_data, periods=3)
        elif method == 'prophet' and PROPHET_INSTALLED:
            forecast_df = forecast_prophet(train_data, periods=3)
        elif method == 'ensemble' and STATSMODELS_INSTALLED and PROPHET_INSTALLED:
            arima_forecast = forecast_arima(train_data, periods=3)
            prophet_forecast = forecast_prophet(train_data, periods=3)
            
            if arima_forecast is not None and prophet_forecast is not None:
                forecast_df = pd.DataFrame({
                    'data': arima_forecast['data'],
                    'quantidade': ((arima_forecast['quantidade'] + prophet_forecast['quantidade']) / 2).round()
                })
            elif arima_forecast is not None:
                forecast_df = arima_forecast
            elif prophet_forecast is not None:
                forecast_df = prophet_forecast
            else:
                forecast_df = forecast_moving_average(train_data, periods=3)
        else:
            forecast_df = forecast_moving_average(train_data, periods=3)
        
        if forecast_df is None:
            return 0
        
        # Calcular erro médio absoluto percentual (MAPE)
        actual = test_data['quantidade'].values
        predicted = forecast_df['quantidade'].values[:len(actual)]
        
        # Evitar divisão por zero
        mape = np.mean(np.abs((actual - predicted) / np.maximum(1, actual))) * 100
        
        # Converter MAPE para precisão (100% - MAPE)
        accuracy = max(0, min(100, 100 - mape))
        
        return accuracy
    except Exception as e:
        logger.error(f"Erro ao calcular precisão da previsão: {str(e)}")
        return 0


def generate_forecast(mola_id, method='auto', periodo='M', grupo_sessao=None):
    """
    Gera uma previsão de demanda para uma mola e salva no banco de dados
    
    Args:
        mola_id: ID da mola
        method: Método de previsão ('auto', 'arima', 'prophet', 'ensemble', 'media_movel')
        periodo: Período da previsão ('S' para semanal, 'M' para mensal, 'T' para trimestral)
        
    Returns:
        Objeto PrevisaoDemanda ou None se falhar
    """
    try:
        mola = Mola.objects.get(id=mola_id)
        hoje = timezone.now().date()
        
        # Definir número de períodos com base no período escolhido
        if periodo == 'S':
            periods = 4  # 4 semanas
            data_fim = hoje + timedelta(days=28)
            metodo_codigo = 'RL' if method == 'prophet' else ('SE' if method == 'arima' else 'MM')
        elif periodo == 'M':
            periods = 3  # 3 meses
            data_fim = hoje + timedelta(days=90)
            metodo_codigo = 'RL' if method == 'prophet' else ('SE' if method == 'arima' else 'MM')
        else:  # Trimestral
            periods = 4  # 4 trimestres (1 ano)
            data_fim = hoje + timedelta(days=365)
            metodo_codigo = 'RL' if method == 'prophet' else ('SE' if method == 'arima' else 'MM')
        
        # Gerar previsão
        forecast_df, accuracy, method_name = forecast_ensemble(mola_id, periods, method)
        
        if forecast_df is None:
            logger.warning(f"Não foi possível gerar previsão para a mola ID {mola_id}")
            return None
        
        # Calcular quantidade total prevista
        quantidade_prevista = int(forecast_df['quantidade'].sum())

        # Arredondar para múltiplos da quantidade por volume
        if mola.quantidade_por_volume and mola.quantidade_por_volume > 0:
            # Arredondar para o próximo múltiplo da quantidade por volume
            multiplo = mola.quantidade_por_volume
            quantidade_prevista = ((quantidade_prevista + multiplo - 1) // multiplo) * multiplo

        # Criar e salvar a previsão
        previsao = PrevisaoDemanda.objects.create(
            mola=mola,
            periodo=periodo,
            metodo=metodo_codigo,
            quantidade_prevista=quantidade_prevista,
            data_inicio=hoje + timedelta(days=1),
            data_fim=data_fim,
            precisao=accuracy,
            grupo_sessao=grupo_sessao
        )
        
        logger.info(f"Previsão gerada para mola {mola.codigo} usando método {method_name} com precisão {accuracy:.1f}%")
        
        return previsao
    except Exception as e:
        logger.error(f"Erro ao gerar previsão para mola ID {mola_id}: {str(e)}")
        return None


def generate_all_forecasts(method='auto', periodo='M', min_data_points=3, grupo_sessao=None):
    """
    Gera previsões para todas as molas com dados históricos suficientes
    
    Args:
        method: Método de previsão
        periodo: Período da previsão
        min_data_points: Número mínimo de pontos de dados históricos necessários
        
    Returns:
        Lista de previsões geradas
    """
    # Obter todas as molas
    molas = Mola.objects.all()
    previsoes = []
    
    # Filtrar molas com dados históricos suficientes e distribuídos no tempo
    for mola in molas:
        from datetime import datetime, timedelta
        from django.db.models import Count
        from django.db.models.functions import TruncMonth
        from .models import ItemPedido

        # Data limite para considerar dados históricos (últimos 12 meses)
        data_limite = datetime.now().date() - timedelta(days=365)

        # Contar movimentações por mês nos últimos 12 meses
        movimentacoes_por_mes = MovimentacaoEstoque.objects.filter(
            mola=mola,
            tipo='S',
            data__gte=data_limite
        ).annotate(
            mes=TruncMonth('data')
        ).values('mes').annotate(
            count=Count('id')
        ).count()

        # Contar pedidos por mês nos últimos 12 meses
        pedidos_por_mes = ItemPedido.objects.filter(
            mola=mola,
            atendido=True,
            pedido__data_pedido__gte=data_limite
        ).annotate(
            mes=TruncMonth('pedido__data_pedido')
        ).values('mes').annotate(
            count=Count('id')
        ).count()

        # Total de meses com vendas
        meses_com_vendas = max(movimentacoes_por_mes, pedidos_por_mes)
        min_meses_necessarios = 2  # Mínimo de 2 meses com vendas

        if meses_com_vendas >= min_meses_necessarios:
            # Gerar previsão
            previsao = generate_forecast(mola.id, method, periodo, grupo_sessao)
            if previsao:
                previsoes.append(previsao)
    
    return previsoes
